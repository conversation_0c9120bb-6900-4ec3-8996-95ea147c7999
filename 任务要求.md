山东协和学院   2024-2025学年第二学期
大数据实时处理技术专业  2022年级（本）
《大数据实时处理技术》期末考试考核方案
一、考试内容
（一）使用RDD编程统计人口平均年龄
1.请编写Spark应用程序，该程序可以在本地文件系统中生成一个数据文件peopleage.txt，数据文件包含若干行（比如1000行，或者100万行等等）记录，每行记录只包含两列数据，第1列是序号，第2列是年龄。效果如下：
1    89
2    67
3    69
4    78
2.请编写Spark应用程序，对本地文件系统中的数据文件peopleage.txt的数据进行处理，计算出所有人口的平均年龄。
（二）通过综合运用大数据处理框架Spark、Hadoop技术，对数据进行存储、处理和分析。供选择题目：（选作其一或自定题目，自定题目的必须征得老师同意）
1.基于咖啡连锁店的Spark数据处理分析（源数据从学习通资料文件夹里下载）
（1）数据预处理
（2）查看咖啡销售量排名并存储
（3）观察咖啡销售量的分布情况，并将输出结果写入本地文件。包括咖啡销售量和state的关系；咖啡销售量和market销售关系；咖啡的平均利润和售价；咖啡的平均利润和售价和销售量的关系；咖啡的平均利润、销售量与其他成本的关系；咖啡属性与平均售价、平均利润、销售量与其他成本的关系；市场规模、市场地域与销售量的关系。
